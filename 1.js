exportPPTX() {
    return $f(this, arguments, (function*(e=!0, {embedFonts: t, isEmbedAllFontText: n, getSubFont: r}={
            embedFonts: !1,
            isEmbedAllFontText: !1,
            getSubFont: null
        }, o=[]) {
            if (e) {
                let e = f.getOptions().changePPTXExprotImage
                    , [r,s] = yield function(e) {
                    return $f(this, null, (function*() {
                            const t = []
                                , n = new Map
                                , r = h.getPages();
                            if (e.length > 0) {
                                const o = r.filter(((t,n)=>e.includes(n))).map((e=>e.toJson(i)));
                                return yield Promise.all(t),
                                    [n, he(o)]
                            }
                            {
                                const e = r.map((e=>e.toJson(i)));
                                return yield Promise.all(t),
                                    [n, he(e)]
                            }
                            function o(e, r) {
                                var o, i;
                                const a = e.getProperties();
                                if (e.type & x1t.CustomElement || e.type & x1t.Image && a.mask && !a.shapeMask) {
                                    const {top: s, left: l, width: u, height: c, src: d, rotate: f, flipH: h, flipV: p} = r;
                                    if (a.mask && 0 == a.mask.url.indexOf("data:image/svg+xml;charset=utf-8,")) {
                                        const e = decodeURIComponent(a.mask.url.substring(33))
                                            , t = (null != (i = null == (o = /viewBox=(["'])([^""]+)\1/.exec(e)) ? void 0 : o[2]) ? i : `0 0 ${u} ${c}`).split(" ").map((e=>Math.round(parseFloat(e))))
                                            , n = [];
                                        let s, l = /<path[^>]+?d=(["'])([^"']+)\1/g, d = t[2], f = t[3], h = d < 12700;
                                        for (h && (d *= 12700,
                                            f *= 12700); s = l.exec(e); )
                                            n.push({
                                                w: d,
                                                h: f,
                                                path: bon(s[2], h)
                                            });
                                        const p = {
                                            line: {},
                                            geometry: {
                                                info: {
                                                    gd: {},
                                                    av: {},
                                                    paths: n,
                                                    adList: []
                                                }
                                            }
                                        };
                                        return Gf(Hf({}, r), {
                                            shapeMask: p
                                        })
                                    }
                                    const v = {
                                        top: s,
                                        left: l,
                                        width: u,
                                        height: c,
                                        src: d,
                                        type: "image",
                                        imageTransform: [1, 0, 0, 1, 0, 0],
                                        rotate: f,
                                        flipH: h,
                                        flipV: p
                                    };
                                    let g = ie(e).then((e=>{
                                            if (null != e) {
                                                const t = URL.createObjectURL(e);
                                                v.src = t,
                                                    n.set(t, e)
                                            }
                                            return e
                                        }
                                    ));
                                    return t.push(g),
                                        v
                                }
                                return r
                            }
                            function i(e, t) {
                                return o(e, j0t(e, t, o))
                            }
                        }
                    ))
                }(o);
                return yield sen(s, {
                    loadFile(t) {
                        return $f(this, null, (function*() {
                                if (r.has(t))
                                    return [r.get(t), "png"];
                                try {
                                    let n = e ? yield e(t) : t
                                        , [r,o] = yield E0t(n);
                                    return [x0t.get(r), o]
                                } catch (EY) {
                                    return [yield new Promise(((e,t)=>$f(this, null, (function*() {
                                            try {
                                                const {canvas: t} = yield T(L0t({
                                                    type: "image",
                                                    imageTransform: [1, 0, 0, 1, 0, 0],
                                                    width: 100,
                                                    height: 100,
                                                    src: 'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg"></svg>'
                                                }, !0, f), 1);
                                                t.toBlob((t=>{
                                                        e(t)
                                                    }
                                                ), "image/png")
                                            } catch (n) {
                                                t(n)
                                            }
                                        }
                                    )))), "png"]
                                }
                            }
                        ))
                    },
                    exportImage(e) {
                        return $f(this, null, (function*() {
                                throw new Error("未加载数据")
                            }
                        ))
                    },
                    getFont: i,
                    hasFont: a,
                    embedFonts: t,
                    isEmbedAllFontText: n
                })
            }
            {
                const e = h.getPages()
                    , r = new Map;
                let o = []
                    , s = 0;
                for (let t of e) {
                    const e = yield ve(s, {
                        scale: 2
                    });
                    let n = URL.createObjectURL(e);
                    r.set(n, e);
                    const i = t.toJson(F0t);
                    i.elements = [{
                        type: "image",
                        src: n,
                        left: 0,
                        top: 0,
                        width: l,
                        height: u,
                        imageTransform: [1, 0, 0, 1, 0, 0]
                    }],
                        o.push(i),
                        s++
                }
                return yield sen(he(o), {
                    loadFile(e) {
                        return $f(this, null, (function*() {
                                var t;
                                const n = null != (t = r.get(e)) ? t : yield new Promise(((e,t)=>$f(this, null, (function*() {
                                        try {
                                            const {canvas: t} = yield T(L0t({
                                                type: "image",
                                                imageTransform: [1, 0, 0, 1, 0, 0],
                                                width: 100,
                                                height: 100,
                                                src: 'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg"></svg>'
                                            }, !0, f), 1);
                                            t.toBlob((t=>{
                                                    e(t)
                                                }
                                            ), "image/png")
                                        } catch (EY) {
                                            t(EY)
                                        }
                                    }
                                ))));
                                return [n, "png"]
                            }
                        ))
                    },
                    exportImage(e) {
                        return $f(this, null, (function*() {
                                throw new Error("未加载数据")
                            }
                        ))
                    },
                    getFont: i,
                    hasFont: a,
                    embedFonts: t,
                    isEmbedAllFontText: n
                })
            }
            function i(e, t) {
                return $f(this, null, (function*() {
                        const n = Ben(e);
                        if (r) {
                            const e = String.fromCharCode(...t)
                                , o = yield r(n.url, e);
                            return [yield(yield fetch(o)).blob(), "eot"]
                        }
                        let[o,i] = yield n.loadData;
                        return "truetype" === i && (i = "ttf"),
                            [n.blob, i]
                    }
                ))
            }
            function a(e) {
                return console.log(e),
                void 0 !== Ben(e)
            }
        }
    ))
},
updateOptions(e) {
    const t = f.getOptions()
        , n = Hf(Hf({}, t), e);
    f.setOptions(n)
},
onTableCellChose: e=>f.EE.on("tableCellChose", e),
    offTableCellChose: e=>f.EE.off("tableCellChose", e),
    onTextFocusChange: e=>f.EE.on("TextInputFocus", e),
    isPointInCanvas: (e,t)=>q(e, t),
    destroyTools() {
    var e, t;
    return null == (t = null == (e = f.getEditorTools()) ? void 0 : e.destroyTools) ? void 0 : t.call(e)
},
clearHistory: ()=>f.getHistory().clearHistory(),
    startBatching: function() {
    fe = pe(),
        f.getHistory().startBatching()
},
endBatching: function() {
    const e = f.getHistory();
    null != fe && (e.endBatching({
        historyType: e.HistoryType.BatchUpdate,
        newRecord: pe(),
        oldRecord: fe,
        recordFnc: de
    }),
        fe = null)
}
}
}