class PPTGenerator {
    constructor() {
        this.jsonData = null;
        this.pptx = null;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const generateBtn = document.getElementById('generateBtn');

        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files[0]);
        });

        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelect(files[0]);
            }
        });

        // 生成按钮事件
        generateBtn.addEventListener('click', () => {
            this.generatePPT();
        });
    }

    handleFileSelect(file) {
        if (!file) return;

        if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
            this.showStatus('请选择有效的 JSON 文件', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                this.jsonData = JSON.parse(e.target.result);
                this.showStatus('JSON 文件加载成功', 'success');
                this.showPreview();
                document.getElementById('generateBtn').disabled = false;
            } catch (error) {
                this.showStatus('JSON 文件格式错误: ' + error.message, 'error');
            }
        };
        reader.readAsText(file);
    }

    showStatus(message, type) {
        const status = document.getElementById('status');
        status.textContent = message;
        status.className = `status ${type}`;
        status.style.display = 'block';
        
        if (type === 'success') {
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }
    }

    showPreview() {
        const preview = document.getElementById('preview');
        const previewContent = document.getElementById('previewContent');
        
        if (!this.jsonData) return;

        const layouts = this.jsonData.layouts || [];
        const global = this.jsonData.global || {};
        
        previewContent.innerHTML = `
            <p><strong>版本:</strong> ${this.jsonData.version || 'N/A'}</p>
            <p><strong>页面类型:</strong> ${this.jsonData.pageType || 'N/A'}</p>
            <p><strong>尺寸:</strong> ${global.width || 960} x ${global.height || 540}</p>
            <p><strong>背景:</strong> ${global.background?.data || '默认'}</p>
            <p><strong>布局数量:</strong> ${layouts.length}</p>
            <div class="slide-preview">
                <h4>幻灯片预览:</h4>
                ${layouts.map((layout, index) => `
                    <p>幻灯片 ${index + 1}: ${layout.elements?.length || 0} 个元素</p>
                `).join('')}
            </div>
        `;
        
        preview.style.display = 'block';
    }

    async generatePPT() {
        if (!this.jsonData) {
            this.showStatus('请先加载 JSON 文件', 'error');
            return;
        }

        try {
            this.showProgress(0);
            this.showStatus('正在生成 PowerPoint 文件...', 'info');

            // 初始化 PptxGenJS
            this.pptx = new PptxGenJS();
            
            // 设置全局属性
            const global = this.jsonData.global || {};
            const slideWidth = (global.width || 960) / 96; // 转换为英寸
            const slideHeight = (global.height || 540) / 96; // 转换为英寸
            
            // 设置幻灯片尺寸
            this.pptx.defineLayout({
                name: 'CUSTOM',
                width: slideWidth,
                height: slideHeight
            });
            this.pptx.layout = 'CUSTOM';

            const layouts = this.jsonData.layouts || [];
            
            for (let i = 0; i < layouts.length; i++) {
                await this.createSlide(layouts[i], i);
                this.showProgress(((i + 1) / layouts.length) * 100);
            }

            // 保存文件
            await this.pptx.writeFile({ fileName: 'generated-presentation.pptx' });
            this.showStatus('PowerPoint 文件生成成功！', 'success');
            this.hideProgress();

        } catch (error) {
            console.error('生成 PPT 时出错:', error);
            this.showStatus('生成 PPT 时出错: ' + error.message, 'error');
            this.hideProgress();
        }
    }

    async createSlide(layout, slideIndex) {
        const slide = this.pptx.addSlide();
        
        // 设置背景
        if (layout.bgfill && layout.bgfill.type === 1 && layout.bgfill.color) {
            const bgColor = this.rgbaToHex(layout.bgfill.color.rgba);
            slide.background = { fill: bgColor };
        }

        const elements = layout.elements || [];
        
        for (let element of elements) {
            await this.addElementToSlide(slide, element);
        }
    }

    async addElementToSlide(slide, element) {
        const left = (element.left || 0) / 96; // 转换为英寸
        const top = (element.top || 0) / 96;
        const width = (element.width || 100) / 96;
        const height = (element.height || 100) / 96;

        try {
            if (element.type === 'text') {
                this.addTextElement(slide, element, left, top, width, height);
            } else if (element.type === 'image') {
                await this.addImageElement(slide, element, left, top, width, height);
            }
        } catch (error) {
            console.warn('添加元素时出错:', error);
        }
    }

    addTextElement(slide, element, left, top, width, height) {
        const contents = element.contents || [];
        let text = '';
        let textOptions = {
            x: left,
            y: top,
            w: width,
            h: height,
            align: this.getTextAlign(element.textAlign),
            valign: this.getVerticalAlign(element.verticalType),
            fontSize: 18,
            color: '000000'
        };

        if (contents.length > 0) {
            const content = contents[0];
            text = content.content || '';
            
            if (content.fontSize) {
                textOptions.fontSize = content.fontSize;
            }
            
            if (content.fontFamily) {
                textOptions.fontFace = content.fontFamily;
            }
            
            if (content.color) {
                textOptions.color = content.color.replace('rgba(', '').replace(')', '').split(',').slice(0, 3).map(c => 
                    Math.round(parseFloat(c.trim())).toString(16).padStart(2, '0')
                ).join('');
            }
            
            if (content.fontFill && content.fontFill.color && content.fontFill.color.rgba) {
                textOptions.color = this.rgbaToHex(content.fontFill.color.rgba);
            }
        }

        // 设置形状背景
        if (element.shapeEffects) {
            const bgEffect = element.shapeEffects.find(effect => effect.type === 'background');
            if (bgEffect && bgEffect.data && bgEffect.data.data && bgEffect.data.data.rgba) {
                textOptions.fill = { color: this.rgbaToHex(bgEffect.data.data.rgba) };
            }
        }

        slide.addText(text, textOptions);
    }

    async addImageElement(slide, element, left, top, width, height) {
        if (!element.src) return;

        try {
            // 如果是网络图片，尝试添加
            if (element.src.startsWith('http')) {
                slide.addImage({
                    path: element.src,
                    x: left,
                    y: top,
                    w: width,
                    h: height
                });
            }
        } catch (error) {
            console.warn('添加图片失败:', error);
            // 如果图片加载失败，添加一个占位符文本
            slide.addText('图片加载失败', {
                x: left,
                y: top,
                w: width,
                h: height,
                align: 'center',
                valign: 'middle',
                fontSize: 12,
                color: 'FF0000'
            });
        }
    }

    rgbaToHex(rgba) {
        if (!rgba || rgba.length < 3) return '000000';
        
        const r = Math.round(rgba[0] * 255).toString(16).padStart(2, '0');
        const g = Math.round(rgba[1] * 255).toString(16).padStart(2, '0');
        const b = Math.round(rgba[2] * 255).toString(16).padStart(2, '0');
        
        return r + g + b;
    }

    getTextAlign(align) {
        switch (align) {
            case 'left': return 'left';
            case 'right': return 'right';
            case 'center': return 'center';
            default: return 'center';
        }
    }

    getVerticalAlign(valign) {
        switch (valign) {
            case 'top': return 'top';
            case 'bottom': return 'bottom';
            case 'center': return 'middle';
            default: return 'middle';
        }
    }

    showProgress(percent) {
        const progress = document.getElementById('progress');
        const progressBar = document.getElementById('progressBar');
        progress.style.display = 'block';
        progressBar.style.width = percent + '%';
    }

    hideProgress() {
        const progress = document.getElementById('progress');
        progress.style.display = 'none';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new PPTGenerator();
});
