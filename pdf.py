import json
import requests
from pptx import Presentation
from pptx.util import Inches

# 下载 JSON 数据
url = "https://aippt-domestic.aippt.com/aippt-server/personal/other/22803/4560741/20250828161341ckdwaqo.json"
response = requests.get(url)
data = response.json()
print(data)

# 创建演示文稿对象
prs = Presentation()

# 遍历 JSON 数据中的页面
for page in data.get("layouts", []):
    slide = prs.slides.add_slide(prs.slide_layouts[5])  # 使用空白布局

    # 添加背景
    if page.get("background"):
        bg = page["background"]
        if bg.get("type") == "image":
            img_url = bg["data"]
            img_path = "background.jpg"
            with open(img_path, "wb") as f:
                f.write(requests.get(img_url).content)
            slide.shapes.add_picture(img_path, 0, 0, width=prs.slide_width, height=prs.slide_height)

    # 添加元素
    for element in page.get("elements", []):
        if element["type"] == "text":
            slide.shapes.add_textbox(Inches(element["left"] / 96), <PERSON>hes(element["top"] / 96),
                                     Inches(element["width"] / 96), Inches(element["height"] / 96))
            textbox = slide.shapes[-1].text_frame
            textbox.text = element["text"]
        elif element["type"] == "image":
            img_url = element["src"]
            img_path = "image.jpg"
            with open(img_path, "wb") as f:
                f.write(requests.get(img_url).content)
            slide.shapes.add_picture(img_path, Inches(element["left"] / 96), Inches(element["top"] / 96),
                                     width=Inches(element["width"] / 96), height=Inches(element["height"] / 96))

# 保存演示文稿
prs.save("output.pptx")
