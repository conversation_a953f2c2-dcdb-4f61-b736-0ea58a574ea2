<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接生成 PPT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .generate-btn {
            background-color: #28a745;
            color: white;
            padding: 20px 40px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 20px;
            margin: 20px;
        }
        .generate-btn:hover {
            background-color: #218838;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
        .info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>从 1.json 生成 PowerPoint</h1>
        
        <div class="info">
            <h3>说明：</h3>
            <p>这个工具会直接读取同目录下的 <strong>1.json</strong> 文件，并生成对应的 PowerPoint 演示文稿。</p>
            <p>生成的文件将自动下载为 <strong>presentation.pptx</strong></p>
        </div>
        
        <button class="generate-btn" id="generateBtn">
            🎯 生成 PowerPoint 文件
        </button>
        
        <div class="progress" id="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="status" id="status"></div>
    </div>

    <!-- 引入 PptxGenJS 库 -->
    <script src="https://cdn.jsdelivr.net/npm/pptxgenjs@3.12.0/dist/pptxgen.bundle.js"></script>
    
    <script>
        class DirectPPTGenerator {
            constructor() {
                this.initializeEventListeners();
            }

            initializeEventListeners() {
                const generateBtn = document.getElementById('generateBtn');
                generateBtn.addEventListener('click', () => {
                    this.generatePPTFromLocalFile();
                });
            }

            async generatePPTFromLocalFile() {
                try {
                    this.showStatus('正在加载 1.json 文件...', 'info');
                    
                    // 加载本地 JSON 文件
                    const response = await fetch('./1.json');
                    if (!response.ok) {
                        throw new Error('无法加载 1.json 文件，请确保文件在同一目录下');
                    }
                    
                    const jsonData = await response.json();
                    this.showStatus('JSON 文件加载成功，开始生成 PPT...', 'info');
                    
                    await this.generatePPT(jsonData);
                    
                } catch (error) {
                    console.error('错误:', error);
                    this.showStatus('错误: ' + error.message, 'error');
                }
            }

            async generatePPT(jsonData) {
                try {
                    this.showProgress(0);
                    
                    // 初始化 PptxGenJS
                    const pptx = new PptxGenJS();
                    
                    // 设置全局属性
                    const global = jsonData.global || {};
                    const slideWidth = (global.width || 960) / 96; // 转换为英寸
                    const slideHeight = (global.height || 540) / 96; // 转换为英寸
                    
                    // 设置幻灯片尺寸
                    pptx.defineLayout({
                        name: 'CUSTOM',
                        width: slideWidth,
                        height: slideHeight
                    });
                    pptx.layout = 'CUSTOM';

                    const layouts = jsonData.layouts || [];
                    this.showStatus(`找到 ${layouts.length} 个幻灯片，正在处理...`, 'info');
                    
                    for (let i = 0; i < layouts.length; i++) {
                        await this.createSlide(pptx, layouts[i], i);
                        this.showProgress(((i + 1) / layouts.length) * 80); // 80% 用于处理幻灯片
                    }

                    this.showStatus('正在保存文件...', 'info');
                    this.showProgress(90);
                    
                    // 保存文件
                    await pptx.writeFile({ fileName: 'presentation.pptx' });
                    
                    this.showProgress(100);
                    this.showStatus('PowerPoint 文件生成成功！文件已开始下载。', 'success');
                    
                    setTimeout(() => {
                        this.hideProgress();
                    }, 2000);

                } catch (error) {
                    console.error('生成 PPT 时出错:', error);
                    this.showStatus('生成 PPT 时出错: ' + error.message, 'error');
                    this.hideProgress();
                }
            }

            async createSlide(pptx, layout, slideIndex) {
                const slide = pptx.addSlide();
                
                // 设置背景
                if (layout.bgfill && layout.bgfill.type === 1 && layout.bgfill.color) {
                    const bgColor = this.rgbaToHex(layout.bgfill.color.rgba);
                    slide.background = { fill: bgColor };
                }

                const elements = layout.elements || [];
                console.log(`幻灯片 ${slideIndex + 1}: 处理 ${elements.length} 个元素`);
                
                for (let element of elements) {
                    await this.addElementToSlide(slide, element);
                }
            }

            async addElementToSlide(slide, element) {
                const left = (element.left || 0) / 96; // 转换为英寸
                const top = (element.top || 0) / 96;
                const width = (element.width || 100) / 96;
                const height = (element.height || 100) / 96;

                try {
                    if (element.type === 'text') {
                        this.addTextElement(slide, element, left, top, width, height);
                    } else if (element.type === 'image') {
                        await this.addImageElement(slide, element, left, top, width, height);
                    }
                } catch (error) {
                    console.warn('添加元素时出错:', error);
                }
            }

            addTextElement(slide, element, left, top, width, height) {
                const contents = element.contents || [];
                let text = '';
                let textOptions = {
                    x: left,
                    y: top,
                    w: width,
                    h: height,
                    align: this.getTextAlign(element.textAlign),
                    valign: this.getVerticalAlign(element.verticalType),
                    fontSize: 18,
                    color: '000000'
                };

                if (contents.length > 0) {
                    const content = contents[0];
                    text = content.content || '';
                    
                    if (content.fontSize) {
                        textOptions.fontSize = content.fontSize;
                    }
                    
                    if (content.fontFamily) {
                        textOptions.fontFace = content.fontFamily.replace(' Normal', '');
                    }
                    
                    if (content.fontFill && content.fontFill.color && content.fontFill.color.rgba) {
                        textOptions.color = this.rgbaToHex(content.fontFill.color.rgba);
                    }
                }

                // 设置形状背景
                if (element.shapeEffects) {
                    const bgEffect = element.shapeEffects.find(effect => effect.type === 'background');
                    if (bgEffect && bgEffect.data && bgEffect.data.data) {
                        if (bgEffect.data.data.rgba) {
                            textOptions.fill = { color: this.rgbaToHex(bgEffect.data.data.rgba) };
                        } else if (bgEffect.data.data.color && bgEffect.data.data.color !== 'transparent') {
                            textOptions.fill = { color: bgEffect.data.data.color.replace('#', '') };
                        }
                    }
                }

                // 设置形状边框
                if (element.shapeEffects) {
                    const strokeEffect = element.shapeEffects.find(effect => effect.type === 'stroke');
                    if (strokeEffect && strokeEffect.data && strokeEffect.data.data) {
                        const strokeData = strokeEffect.data.data;
                        if (strokeData.color && strokeData.color !== 'transparent') {
                            textOptions.line = {
                                color: strokeData.color.replace('#', ''),
                                width: strokeData.width || 1
                            };
                        }
                    }
                }

                // 如果文本为空，添加占位符
                if (!text.trim()) {
                    text = ' '; // 空格作为占位符，保持形状可见
                }

                slide.addText(text, textOptions);
            }

            async addImageElement(slide, element, left, top, width, height) {
                if (!element.src) return;

                try {
                    // 如果是网络图片，尝试添加
                    if (element.src.startsWith('http')) {
                        slide.addImage({
                            path: element.src,
                            x: left,
                            y: top,
                            w: width,
                            h: height
                        });
                        console.log('添加图片:', element.src);
                    }
                } catch (error) {
                    console.warn('添加图片失败:', error);
                    // 如果图片加载失败，添加一个占位符
                    slide.addText('图片', {
                        x: left,
                        y: top,
                        w: width,
                        h: height,
                        align: 'center',
                        valign: 'middle',
                        fontSize: 12,
                        color: '666666',
                        fill: { color: 'F0F0F0' }
                    });
                }
            }

            rgbaToHex(rgba) {
                if (!rgba || rgba.length < 3) return '000000';
                
                const r = Math.round(rgba[0] * 255).toString(16).padStart(2, '0');
                const g = Math.round(rgba[1] * 255).toString(16).padStart(2, '0');
                const b = Math.round(rgba[2] * 255).toString(16).padStart(2, '0');
                
                return r + g + b;
            }

            getTextAlign(align) {
                switch (align) {
                    case 'left': return 'left';
                    case 'right': return 'right';
                    case 'center': return 'center';
                    default: return 'center';
                }
            }

            getVerticalAlign(valign) {
                switch (valign) {
                    case 'top': return 'top';
                    case 'bottom': return 'bottom';
                    case 'center': return 'middle';
                    default: return 'middle';
                }
            }

            showStatus(message, type) {
                const status = document.getElementById('status');
                status.textContent = message;
                status.className = `status ${type}`;
                status.style.display = 'block';
                
                if (type === 'success') {
                    setTimeout(() => {
                        status.style.display = 'none';
                    }, 5000);
                }
            }

            showProgress(percent) {
                const progress = document.getElementById('progress');
                const progressBar = document.getElementById('progressBar');
                progress.style.display = 'block';
                progressBar.style.width = percent + '%';
            }

            hideProgress() {
                const progress = document.getElementById('progress');
                progress.style.display = 'none';
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new DirectPPTGenerator();
        });
    </script>
</body>
</html>
