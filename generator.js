const PptxGenJS = require('pptxgenjs');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

class PPTGenerator {
    constructor() {
        this.pptx = new PptxGenJS();
        this.imageCache = new Map();
    }

    async generateFromFile(jsonFilePath, outputPath = 'generated-presentation.pptx') {
        try {
            console.log('🚀 开始生成 PowerPoint 文件...');
            
            // 读取 JSON 文件
            const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));
            console.log('✅ JSON 文件读取成功');
            
            await this.generatePPT(jsonData, outputPath);
            
        } catch (error) {
            console.error('❌ 生成失败:', error.message);
            throw error;
        }
    }

    async generatePPT(jsonData, outputPath) {
        // 设置全局属性
        const global = jsonData.global || {};
        const slideWidth = (global.width || 960) / 96; // 转换为英寸
        const slideHeight = (global.height || 540) / 96;
        
        // 设置幻灯片尺寸
        this.pptx.defineLayout({
            name: 'CUSTOM',
            width: slideWidth,
            height: slideHeight
        });
        this.pptx.layout = 'CUSTOM';

        // 设置演示文稿属性
        this.pptx.author = 'JSON to PPT Generator';
        this.pptx.company = 'Auto Generated';
        this.pptx.subject = 'Generated from JSON data';
        this.pptx.title = 'Presentation';

        const layouts = jsonData.layouts || [];
        console.log(`📄 找到 ${layouts.length} 个幻灯片`);
        
        for (let i = 0; i < layouts.length; i++) {
            console.log(`🔄 处理第 ${i + 1}/${layouts.length} 个幻灯片...`);
            await this.createSlide(layouts[i], i);
        }

        // 保存文件
        console.log('💾 正在保存文件...');
        await this.pptx.writeFile({ fileName: outputPath });
        console.log(`✅ PowerPoint 文件已生成: ${outputPath}`);
    }

    async createSlide(layout, slideIndex) {
        const slide = this.pptx.addSlide();
        
        // 设置幻灯片背景
        await this.setSlideBackground(slide, layout);

        const elements = layout.elements || [];
        console.log(`   📝 处理 ${elements.length} 个元素...`);
        
        for (let j = 0; j < elements.length; j++) {
            try {
                await this.addElementToSlide(slide, elements[j], j);
            } catch (error) {
                console.warn(`   ⚠️  元素 ${j} 处理失败:`, error.message);
            }
        }
    }

    async setSlideBackground(slide, layout) {
        // 处理背景填充
        if (layout.bgfill && layout.bgfill.type === 1 && layout.bgfill.color) {
            const bgColor = this.processColor(layout.bgfill.color);
            if (bgColor) {
                slide.background = { 
                    fill: bgColor.color,
                    transparency: bgColor.transparency || 0
                };
            }
        }
    }

    async addElementToSlide(slide, element, elementIndex) {
        const left = (element.left || 0) / 96; // 转换为英寸
        const top = (element.top || 0) / 96;
        const width = (element.width || 100) / 96;
        const height = (element.height || 100) / 96;

        if (element.type === 'text') {
            this.addTextElement(slide, element, left, top, width, height);
        } else if (element.type === 'image') {
            await this.addImageElement(slide, element, left, top, width, height, elementIndex);
        }
    }

    addTextElement(slide, element, left, top, width, height) {
        const contents = element.contents || [];
        let text = '';
        
        // 基础文本选项
        let textOptions = {
            x: left,
            y: top,
            w: width,
            h: height,
            align: this.getTextAlign(element.textAlign),
            valign: this.getVerticalAlign(element.verticalType),
            fontSize: 18,
            color: '000000',
            autoFit: element.autoFit || false
        };

        // 处理文本内容和字体样式
        if (contents.length > 0) {
            const content = contents[0];
            text = content.content || '';
            
            if (content.fontSize) {
                textOptions.fontSize = content.fontSize;
            }
            
            if (content.fontFamily) {
                textOptions.fontFace = content.fontFamily.replace(' Normal', '').replace(' CN', '');
            }
            
            // 字体颜色
            if (content.fontFill && content.fontFill.color && content.fontFill.color.rgba) {
                textOptions.color = this.rgbaToHex(content.fontFill.color.rgba);
            } else if (content.color) {
                textOptions.color = this.parseColor(content.color);
            }
            
            // 字体粗细
            if (content.fontWeight && content.fontWeight > 400) {
                textOptions.bold = true;
            }
            
            // 字体样式
            if (content.fontStyle === 'italic') {
                textOptions.italic = true;
            }
        }

        // 处理文本布局属性
        if (element.lineHeight && element.lineHeight !== 1) {
            textOptions.lineSpacing = Math.round(element.lineHeight * 100);
        }
        
        if (element.letterSpacing && element.letterSpacing !== 0) {
            textOptions.charSpacing = element.letterSpacing;
        }

        // 处理内边距
        if (element.pad && element.pad.length >= 4) {
            textOptions.margin = [
                element.pad[0] / 96, // top
                element.pad[1] / 96, // right  
                element.pad[2] / 96, // bottom
                element.pad[3] / 96  // left
            ];
        }

        // 应用形状样式
        this.applyShapeStyles(textOptions, element);

        // 如果文本为空，添加占位符以保持形状可见
        if (!text.trim()) {
            text = ' ';
        }

        slide.addText(text, textOptions);
    }

    applyShapeStyles(textOptions, element) {
        const shape = element.shape;
        if (!shape) return;

        // 处理形状几何类型
        if (shape.geometry) {
            const geometryType = shape.geometry.type;
            
            switch (geometryType) {
                case 'ellipse':
                    textOptions.shape = this.pptx.ShapeType.oval;
                    break;
                case 'round2SameRect':
                    textOptions.shape = this.pptx.ShapeType.roundRect;
                    // 处理圆角
                    if (shape.geometry.avList && shape.geometry.avList.adj1) {
                        textOptions.rectRadius = Math.min(shape.geometry.avList.adj1 / 10000, 0.5);
                    }
                    break;
                case 'rect':
                default:
                    textOptions.shape = this.pptx.ShapeType.rect;
                    break;
            }
        }

        // 处理填充
        this.applyFillStyles(textOptions, shape, element);
        
        // 处理边框
        this.applyLineStyles(textOptions, shape, element);
        
        // 处理阴影
        this.applyShadowStyles(textOptions, shape, element);
    }

    applyFillStyles(textOptions, shape, element) {
        // 优先处理 shapeEffects 中的背景
        if (element.shapeEffects) {
            const bgEffect = element.shapeEffects.find(effect => effect.type === 'background');
            if (bgEffect && bgEffect.data && bgEffect.data.data) {
                if (bgEffect.data.data.rgba) {
                    const bgColor = this.processColorRGBA(bgEffect.data.data.rgba, bgEffect.data.data.opacity);
                    textOptions.fill = { color: bgColor.color };
                    if (bgColor.transparency) {
                        textOptions.fill.transparency = bgColor.transparency;
                    }
                    return;
                } else if (bgEffect.data.data.color && bgEffect.data.data.color !== 'transparent') {
                    textOptions.fill = { color: bgEffect.data.data.color.replace('#', '') };
                    return;
                }
            }
        }

        // 处理 shape.fill
        if (shape.fill) {
            if (shape.fill.type === 1 && shape.fill.color) {
                // 纯色填充
                const fillColor = this.processColor(shape.fill.color);
                if (fillColor) {
                    textOptions.fill = { color: fillColor.color };
                    if (fillColor.transparency) {
                        textOptions.fill.transparency = fillColor.transparency;
                    }
                }
            } else if (shape.fill.type === 2 && shape.fill.gradient) {
                // 渐变填充 - PptxGenJS 对渐变支持有限，使用第一个颜色
                const gradient = shape.fill.gradient;
                if (gradient.colors && gradient.colors.length >= 1) {
                    const firstColor = this.processColor(gradient.colors[0]);
                    if (firstColor) {
                        textOptions.fill = { color: firstColor.color };
                        if (firstColor.transparency) {
                            textOptions.fill.transparency = firstColor.transparency;
                        }
                    }
                }
            }
        }
    }

    applyLineStyles(textOptions, shape, element) {
        // 优先处理 shapeEffects 中的边框
        if (element.shapeEffects) {
            const strokeEffect = element.shapeEffects.find(effect => effect.type === 'stroke');
            if (strokeEffect && strokeEffect.data && strokeEffect.data.data) {
                const strokeData = strokeEffect.data.data;
                if (strokeData.color && strokeData.color !== 'transparent') {
                    textOptions.line = {
                        color: strokeData.color.replace('#', ''),
                        width: strokeData.width || 1
                    };
                    if (strokeData.opacity && strokeData.opacity < 1) {
                        textOptions.line.transparency = Math.round((1 - strokeData.opacity) * 100);
                    }
                    return;
                }
            }
        }

        // 处理 shape.line
        if (shape.line && shape.line.stroke && shape.line.stroke.type === 1) {
            const strokeColor = this.processColor(shape.line.stroke.color);
            if (strokeColor && strokeColor.color !== 'transparent') {
                textOptions.line = {
                    color: strokeColor.color,
                    width: shape.line.strokeWidth || 1
                };
                if (strokeColor.transparency) {
                    textOptions.line.transparency = strokeColor.transparency;
                }
            }
        }
    }

    applyShadowStyles(textOptions, shape, element) {
        // 处理形状特效中的阴影
        if (shape.effects && shape.effects.outerShdw) {
            const shadow = shape.effects.outerShdw;
            textOptions.shadow = {
                type: 'outer',
                color: shadow.color ? this.processColor(shadow.color).color : '808080',
                blur: shadow.blurRad ? Math.round(shadow.blurRad / 12700) : 3,
                offset: shadow.dist ? Math.round(shadow.dist / 12700) : 2,
                angle: shadow.dir ? Math.round(shadow.dir / 60000) : 45
            };
        }

        // 处理 shapeEffects 中的阴影
        else if (element.shapeEffects) {
            const shadowEffect = element.shapeEffects.find(effect => effect.type === 'shadow');
            if (shadowEffect && shadowEffect.data) {
                textOptions.shadow = {
                    type: 'outer',
                    color: '808080',
                    blur: 3,
                    offset: 2,
                    angle: 45
                };
            }
        }
    }

    async addImageElement(slide, element, left, top, width, height, elementIndex) {
        if (!element.src) return;

        try {
            let imageOptions = {
                x: left,
                y: top,
                w: width,
                h: height
            };

            // 处理图片透明度
            if (element.opacity && element.opacity < 1) {
                imageOptions.transparency = Math.round((1 - element.opacity) * 100);
            }

            // 处理图片变换
            if (element.imageTransform && element.imageTransform.length >= 6) {
                const transform = element.imageTransform;
                // 处理缩放
                if (transform[0] !== 1 || transform[3] !== 1) {
                    imageOptions.w = width * Math.abs(transform[0]);
                    imageOptions.h = height * Math.abs(transform[3]);
                }
            }

            // 处理翻转
            if (element.flipH) {
                imageOptions.flipH = true;
            }
            if (element.flipV) {
                imageOptions.flipV = true;
            }

            // 处理旋转
            if (element.rotate) {
                imageOptions.rotate = element.rotate;
            }

            // 下载并添加图片
            if (element.src.startsWith('http')) {
                const imagePath = await this.downloadImage(element.src, elementIndex);
                if (imagePath) {
                    imageOptions.path = imagePath;
                    slide.addImage(imageOptions);
                    console.log(`     ✅ 图片已添加: ${element.src}`);
                } else {
                    this.addImagePlaceholder(slide, left, top, width, height, '图片下载失败');
                }
            } else {
                this.addImagePlaceholder(slide, left, top, width, height, '不支持的图片格式');
            }
        } catch (error) {
            console.warn(`     ⚠️  图片处理失败:`, error.message);
            this.addImagePlaceholder(slide, left, top, width, height, '图片加载失败');
        }
    }

    addImagePlaceholder(slide, left, top, width, height, message) {
        slide.addText(message, {
            x: left,
            y: top,
            w: width,
            h: height,
            align: 'center',
            valign: 'middle',
            fontSize: Math.min(width * 96 / 10, height * 96 / 10, 12),
            color: '666666',
            fill: { color: 'F0F0F0' },
            shape: this.pptx.ShapeType.rect
        });
    }

    async downloadImage(url, elementIndex) {
        try {
            // 检查缓存
            if (this.imageCache.has(url)) {
                return this.imageCache.get(url);
            }

            const response = await axios({
                method: 'GET',
                url: url,
                responseType: 'arraybuffer',
                timeout: 10000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });

            // 确定文件扩展名
            const contentType = response.headers['content-type'] || '';
            let ext = 'jpg';
            if (contentType.includes('png')) ext = 'png';
            else if (contentType.includes('gif')) ext = 'gif';
            else if (contentType.includes('webp')) ext = 'webp';

            // 保存临时文件
            const tempDir = path.join(__dirname, 'temp');
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }

            const filename = `image_${elementIndex}_${Date.now()}.${ext}`;
            const filepath = path.join(tempDir, filename);

            fs.writeFileSync(filepath, response.data);

            // 缓存文件路径
            this.imageCache.set(url, filepath);

            return filepath;
        } catch (error) {
            console.warn(`     ⚠️  图片下载失败 ${url}:`, error.message);
            return null;
        }
    }

    // 颜色处理方法
    processColor(colorObj) {
        if (!colorObj) return null;

        let color = '000000';
        let transparency = 0;

        if (colorObj.rgba) {
            color = this.rgbaToHex(colorObj.rgba);
            if (colorObj.rgba.length > 3) {
                transparency = Math.round((1 - colorObj.rgba[3]) * 100);
            }
        }

        // 处理颜色变换
        if (colorObj.transform && colorObj.transform.length > 0) {
            for (let transform of colorObj.transform) {
                if (transform.type === 'alpha') {
                    transparency = Math.round((1 - parseInt(transform.value) / 100000) * 100);
                } else if (transform.type === 'lumMod') {
                    // 亮度调制 - 简化处理
                    const factor = parseInt(transform.value) / 100000;
                    if (factor < 1) {
                        // 变暗
                        const rgb = this.hexToRgb(color);
                        if (rgb) {
                            rgb.r = Math.round(rgb.r * factor);
                            rgb.g = Math.round(rgb.g * factor);
                            rgb.b = Math.round(rgb.b * factor);
                            color = this.rgbToHex(rgb.r, rgb.g, rgb.b);
                        }
                    }
                } else if (transform.type === 'lumOff') {
                    // 亮度偏移 - 简化处理
                    const offset = parseInt(transform.value) / 100000;
                    if (offset > 0) {
                        // 变亮
                        const rgb = this.hexToRgb(color);
                        if (rgb) {
                            rgb.r = Math.min(255, Math.round(rgb.r + (255 - rgb.r) * offset));
                            rgb.g = Math.min(255, Math.round(rgb.g + (255 - rgb.g) * offset));
                            rgb.b = Math.min(255, Math.round(rgb.b + (255 - rgb.b) * offset));
                            color = this.rgbToHex(rgb.r, rgb.g, rgb.b);
                        }
                    }
                }
            }
        }

        return { color, transparency: transparency > 0 ? transparency : null };
    }

    processColorRGBA(rgba, opacity = 1) {
        const color = this.rgbaToHex(rgba);
        let transparency = 0;

        if (rgba.length > 3) {
            transparency = Math.round((1 - rgba[3]) * 100);
        } else if (opacity < 1) {
            transparency = Math.round((1 - opacity) * 100);
        }

        return { color, transparency: transparency > 0 ? transparency : null };
    }

    // 工具方法
    rgbaToHex(rgba) {
        if (!rgba || rgba.length < 3) return '000000';

        const r = Math.round(rgba[0] * 255).toString(16).padStart(2, '0');
        const g = Math.round(rgba[1] * 255).toString(16).padStart(2, '0');
        const b = Math.round(rgba[2] * 255).toString(16).padStart(2, '0');

        return r + g + b;
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    rgbToHex(r, g, b) {
        return ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    parseColor(colorStr) {
        if (colorStr.startsWith('rgba(')) {
            const matches = colorStr.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
            if (matches) {
                const r = parseInt(matches[1]);
                const g = parseInt(matches[2]);
                const b = parseInt(matches[3]);
                return this.rgbToHex(r, g, b);
            }
        } else if (colorStr.startsWith('rgb(')) {
            const matches = colorStr.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
            if (matches) {
                const r = parseInt(matches[1]);
                const g = parseInt(matches[2]);
                const b = parseInt(matches[3]);
                return this.rgbToHex(r, g, b);
            }
        } else if (colorStr.startsWith('#')) {
            return colorStr.replace('#', '');
        }
        return '000000';
    }

    getTextAlign(align) {
        switch (align) {
            case 'left': return 'left';
            case 'right': return 'right';
            case 'center': return 'center';
            case 'justify': return 'justify';
            default: return 'center';
        }
    }

    getVerticalAlign(valign) {
        switch (valign) {
            case 'top': return 'top';
            case 'bottom': return 'bottom';
            case 'center': return 'middle';
            case 'middle': return 'middle';
            default: return 'middle';
        }
    }

    // 清理临时文件
    cleanup() {
        const tempDir = path.join(__dirname, 'temp');
        if (fs.existsSync(tempDir)) {
            const files = fs.readdirSync(tempDir);
            files.forEach(file => {
                try {
                    fs.unlinkSync(path.join(tempDir, file));
                } catch (error) {
                    console.warn(`清理临时文件失败: ${file}`);
                }
            });

            try {
                fs.rmdirSync(tempDir);
            } catch (error) {
                console.warn('清理临时目录失败');
            }
        }
    }
}

// 主函数
async function main() {
    const generator = new PPTGenerator();

    try {
        // 检查命令行参数
        const args = process.argv.slice(2);
        const inputFile = args[0] || '1.json';
        const outputFile = args[1] || 'generated-presentation.pptx';

        console.log(`📁 输入文件: ${inputFile}`);
        console.log(`📁 输出文件: ${outputFile}`);

        // 检查输入文件是否存在
        if (!fs.existsSync(inputFile)) {
            throw new Error(`输入文件不存在: ${inputFile}`);
        }

        // 生成 PPT
        await generator.generateFromFile(inputFile, outputFile);

        console.log('🎉 生成完成！');

    } catch (error) {
        console.error('💥 程序执行失败:', error.message);
        process.exit(1);
    } finally {
        // 清理临时文件
        generator.cleanup();
    }
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
    main();
}

module.exports = PPTGenerator;
