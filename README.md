# JSON to PowerPoint Generator

一个强大的 Node.js 工具，可以将 JSON 格式的演示文稿数据转换为完整的 PowerPoint 文件，保留所有样式和格式。

## 功能特点

✅ **完整样式支持**
- 文本样式（字体、颜色、大小、粗体、斜体）
- 形状样式（矩形、圆形、圆角矩形）
- 背景填充（纯色、渐变、透明度）
- 边框样式（颜色、宽度、透明度）
- 阴影效果
- 文本对齐和布局

✅ **图片处理**
- 自动下载网络图片
- 支持透明度、翻转、旋转
- 图片变换和缩放
- 失败时自动添加占位符

✅ **高级功能**
- 多种形状类型支持
- 颜色变换处理（亮度、透明度）
- 内边距和行高设置
- 临时文件自动清理

## 安装

1. 确保已安装 Node.js (版本 14 或更高)

2. 安装依赖包：
```bash
npm install
```

## 使用方法

### 基本用法

```bash
# 使用默认文件名（1.json -> generated-presentation.pptx）
npm start

# 或者直接运行
node generator.js
```

### 指定文件名

```bash
# 指定输入和输出文件
node generator.js input.json output.pptx

# 只指定输入文件
node generator.js my-presentation.json
```

### 命令行参数

- `第一个参数`: 输入的 JSON 文件路径（默认: `1.json`）
- `第二个参数`: 输出的 PPT 文件路径（默认: `generated-presentation.pptx`）

## JSON 文件格式

支持的 JSON 结构包含以下主要部分：

```json
{
  "version": "0.0.23",
  "pageType": "page",
  "global": {
    "width": 960,
    "height": 540,
    "background": {
      "type": "color",
      "data": "#FFFFFF"
    }
  },
  "layouts": [
    {
      "bgfill": { ... },
      "elements": [
        {
          "type": "text",
          "left": 0,
          "top": 0,
          "width": 100,
          "height": 50,
          "contents": [
            {
              "content": "Hello World",
              "fontSize": 18,
              "fontFamily": "Arial",
              "color": "rgba(0,0,0,1)"
            }
          ],
          "shape": {
            "geometry": { "type": "rect" },
            "fill": { ... },
            "line": { ... }
          }
        }
      ]
    }
  ]
}
```

## 支持的元素类型

### 文本元素
- 基本文本内容和样式
- 字体族、大小、颜色
- 粗体、斜体
- 文本对齐（左、中、右、两端对齐）
- 垂直对齐（上、中、下）
- 行高和字间距
- 内边距

### 形状样式
- **矩形** (`rect`)
- **圆形/椭圆** (`ellipse`)
- **圆角矩形** (`round2SameRect`)
- 填充颜色和透明度
- 边框样式
- 阴影效果

### 图片元素
- 网络图片自动下载
- 透明度设置
- 翻转和旋转
- 尺寸变换

## 输出示例

运行成功后会看到类似输出：

```
🚀 开始生成 PowerPoint 文件...
✅ JSON 文件读取成功
📄 找到 12 个幻灯片
🔄 处理第 1/12 个幻灯片...
   📝 处理 6 个元素...
     ✅ 图片已添加: https://example.com/image.png
🔄 处理第 2/12 个幻灯片...
...
💾 正在保存文件...
✅ PowerPoint 文件已生成: generated-presentation.pptx
🎉 生成完成！
```

## 注意事项

1. **网络连接**: 需要网络连接来下载图片资源
2. **图片格式**: 支持 PNG、JPG、GIF、WebP 格式
3. **临时文件**: 程序会自动清理下载的临时图片文件
4. **内存使用**: 大量图片可能占用较多内存
5. **超时设置**: 图片下载超时时间为 10 秒

## 故障排除

### 常见问题

**Q: 图片无法显示？**
A: 检查图片 URL 是否可访问，或网络连接是否正常

**Q: 样式丢失？**
A: 确保 JSON 文件包含完整的样式信息，特别是 `shape` 和 `shapeEffects` 部分

**Q: 文件生成失败？**
A: 检查 JSON 文件格式是否正确，查看控制台错误信息

**Q: 字体显示异常？**
A: 确保目标系统安装了相应字体，或使用系统默认字体

## 技术栈

- **Node.js**: 运行环境
- **pptxgenjs**: PowerPoint 文件生成
- **axios**: HTTP 请求处理
- **sharp**: 图片处理（可选）

## 许可证

MIT License
